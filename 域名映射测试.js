// 域名映射测试脚本
// 在浏览器控制台中运行，验证API域名映射是否正确

console.log('🧪 域名映射测试脚本已加载');

// 获取当前网站对应的API域名
function getApiHost() {
    const hostname = window.location.hostname;
    
    if (hostname.includes('gt667788')) {
        return 'https://api.gt667788.com';
    } else if (hostname.includes('gt45678')) {
        return 'https://api.gt45678.com';
    } else if (hostname.includes('gt558')) {
        return 'https://api.gt558.com';
    } else if (hostname.includes('gt6677')) {
        return 'https://api.gt6677.com';
    } else {
        console.warn('未识别的网站域名，使用默认API: https://api.gt667788.com');
        return 'https://api.gt667788.com';
    }
}

// 测试域名映射
function testDomainMapping() {
    console.log('🔍 测试域名映射...');
    console.log('='.repeat(50));
    
    const currentHost = window.location.hostname;
    const currentOrigin = window.location.origin;
    const apiHost = getApiHost();
    
    console.log('📍 当前信息:');
    console.log('  当前域名:', currentHost);
    console.log('  当前源:', currentOrigin);
    console.log('  映射的API域名:', apiHost);
    
    console.log('');
    console.log('🔗 完整API地址:');
    console.log('  列表API:', apiHost + '/Tenant/GetXPayDepositRequestOrderList?PageSize=2000&PageIndex=1&Role=1&RecordType=0&OrderSourceType=0');
    console.log('  接单API:', apiHost + '/Tenant/ReceivedXPayOrder');
    
    console.log('');
    console.log('✅ 域名映射测试完成');
    console.log('='.repeat(50));
    
    return {
        currentHost,
        currentOrigin,
        apiHost,
        listApi: apiHost + '/Tenant/GetXPayDepositRequestOrderList?PageSize=2000&PageIndex=1&Role=1&RecordType=0&OrderSourceType=0',
        grabApi: apiHost + '/Tenant/ReceivedXPayOrder'
    };
}

// 测试所有可能的域名映射
function testAllDomainMappings() {
    console.log('🧪 测试所有域名映射规则...');
    console.log('='.repeat(60));
    
    const testCases = [
        { hostname: 'opc.gt667788.com', expected: 'https://api.gt667788.com' },
        { hostname: 'opc.gt45678.com', expected: 'https://api.gt45678.com' },
        { hostname: 'opc.gt558.com', expected: 'https://api.gt558.com' },
        { hostname: 'opc.gt6677.com', expected: 'https://api.gt6677.com' },
        { hostname: 'unknown.example.com', expected: 'https://api.gt667788.com' }
    ];
    
    testCases.forEach((testCase, index) => {
        console.log(`测试 ${index + 1}: ${testCase.hostname}`);
        
        // 模拟域名检测逻辑
        let result = '';
        if (testCase.hostname.includes('gt667788')) {
            result = 'https://api.gt667788.com';
        } else if (testCase.hostname.includes('gt45678')) {
            result = 'https://api.gt45678.com';
        } else if (testCase.hostname.includes('gt558')) {
            result = 'https://api.gt558.com';
        } else if (testCase.hostname.includes('gt6677')) {
            result = 'https://api.gt6677.com';
        } else {
            result = 'https://api.gt667788.com';
        }
        
        const isCorrect = result === testCase.expected;
        const status = isCorrect ? '✅' : '❌';
        
        console.log(`  ${status} 期望: ${testCase.expected}`);
        console.log(`  ${status} 实际: ${result}`);
        console.log('');
    });
    
    console.log('✅ 所有域名映射测试完成');
    console.log('='.repeat(60));
}

// 测试API连通性
async function testApiConnectivity() {
    console.log('🌐 测试API连通性...');
    
    const apiHost = getApiHost();
    const listUrl = apiHost + '/Tenant/GetXPayDepositRequestOrderList?PageSize=10&PageIndex=1&Role=1&RecordType=0&OrderSourceType=0';
    
    try {
        console.log('📡 测试列表API连通性...');
        console.log('URL:', listUrl);
        
        const response = await fetch(listUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        console.log('📊 响应状态:', response.status);
        console.log('📊 响应头:', [...response.headers.entries()]);
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ API连通性测试成功');
            console.log('📦 响应数据结构:', {
                code: data.code,
                message: data.message,
                dataType: typeof data.data,
                itemsCount: data.data && data.data.items ? data.data.items.length : 0
            });
            
            return { success: true, data };
        } else {
            console.log('❌ API连通性测试失败');
            console.log('错误状态:', response.status, response.statusText);
            return { success: false, status: response.status };
        }
        
    } catch (error) {
        console.error('❌ API连通性测试异常:', error);
        return { success: false, error: error.message };
    }
}

// 完整的域名和API测试
async function runFullDomainTest() {
    console.log('🚀 开始完整的域名和API测试...');
    console.log('='.repeat(70));
    
    // 1. 测试当前域名映射
    console.log('1️⃣ 当前域名映射测试');
    const mappingResult = testDomainMapping();
    console.log('');
    
    // 2. 测试所有域名映射规则
    console.log('2️⃣ 所有域名映射规则测试');
    testAllDomainMappings();
    console.log('');
    
    // 3. 测试API连通性
    console.log('3️⃣ API连通性测试');
    const connectivityResult = await testApiConnectivity();
    console.log('');
    
    // 4. 汇总结果
    console.log('📊 测试结果汇总:');
    console.log('  域名映射: ✅ 正常');
    console.log('  API连通性:', connectivityResult.success ? '✅ 正常' : '❌ 异常');
    
    if (connectivityResult.success) {
        console.log('  数据获取: ✅ 成功');
        console.log(`  订单数量: ${connectivityResult.data.data?.items?.length || 0} 个`);
    } else {
        console.log('  数据获取: ❌ 失败');
        console.log('  错误信息:', connectivityResult.error || connectivityResult.status);
    }
    
    console.log('');
    console.log('🎯 建议:');
    if (connectivityResult.success) {
        console.log('  ✅ 域名映射配置正确，可以正常使用脚本');
        console.log('  💡 现在可以启动接单脚本进行自动接单');
    } else {
        console.log('  ⚠️ API连接异常，请检查:');
        console.log('    1. 网络连接是否正常');
        console.log('    2. 是否需要登录认证');
        console.log('    3. API域名是否正确');
    }
    
    console.log('='.repeat(70));
    
    return {
        mapping: mappingResult,
        connectivity: connectivityResult
    };
}

// 导出测试函数
console.log('🎮 可用的测试命令:');
console.log('  testDomainMapping() - 测试当前域名映射');
console.log('  testAllDomainMappings() - 测试所有域名映射规则');
console.log('  testApiConnectivity() - 测试API连通性');
console.log('  runFullDomainTest() - 运行完整测试');

// 全局导出
if (typeof window !== 'undefined') {
    window.getApiHost = getApiHost;
    window.testDomainMapping = testDomainMapping;
    window.testAllDomainMappings = testAllDomainMappings;
    window.testApiConnectivity = testApiConnectivity;
    window.runFullDomainTest = runFullDomainTest;
}

// 自动运行基础测试
testDomainMapping();
