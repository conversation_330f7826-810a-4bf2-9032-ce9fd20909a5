// 测试配置文件 - 用于快速测试和调试抢单脚本

// 在浏览器控制台中运行以下代码来测试脚本功能

// 1. 测试配置设置
function testConfig() {
    console.log('🧪 测试配置设置...');
    
    const testConfig = {
        minPrice: 2000,
        maxPrice: 5000,
        checkInterval: 3000,
        maxConcurrent: 3,
        enableBankFilter: true,
        bankFilter: '工商银行',
        listApi: '/api/orders/list',
        grabApi: '/api/orders/grab'
    };
    
    orderGrabber.configManager.saveConfig(testConfig);
    orderGrabber.config = testConfig;
    
    console.log('✅ 配置已更新:', testConfig);
}

// 2. 模拟订单数据测试筛选逻辑
function testOrderFiltering() {
    console.log('🧪 测试订单筛选逻辑...');
    
    const mockOrders = [
        { id: '001', price: 1500, bank: '工商银行', status: 'available' },  // 价格太低
        { id: '002', price: 3000, bank: '工商银行', status: 'available' },  // 符合条件
        { id: '003', price: 4500, bank: '建设银行', status: 'available' },  // 银行不匹配
        { id: '004', price: 6000, bank: '工商银行', status: 'available' },  // 价格太高
        { id: '005', price: 2500, bank: '工商银行', status: 'taken' },     // 状态不可用
        { id: '006', price: 3500, bank: '工商银行', status: 'available' }   // 符合条件
    ];
    
    const filtered = orderGrabber.filterOrders(mockOrders);
    console.log('📊 筛选结果:', filtered);
    console.log(`✅ 从${mockOrders.length}个订单中筛选出${filtered.length}个符合条件的订单`);
    
    return filtered;
}

// 3. 测试API URL构建
function testApiUrls() {
    console.log('🧪 测试API URL构建...');
    
    const listUrl = orderGrabber.getFullApiUrl('/api/orders/list');
    const grabUrl = orderGrabber.getFullApiUrl('/api/orders/grab');
    
    console.log('📡 列表API URL:', listUrl);
    console.log('📡 抢单API URL:', grabUrl);
    
    return { listUrl, grabUrl };
}

// 4. 测试统计功能
function testStats() {
    console.log('🧪 测试统计功能...');
    
    const stats = orderGrabber.getStats();
    console.log('📊 当前统计:', stats);
    
    return stats;
}

// 5. 模拟抢单测试（不实际发送请求）
function simulateGrabOrder() {
    console.log('🧪 模拟抢单测试...');
    
    const mockOrder = {
        id: 'TEST_001',
        price: 3000,
        bank: '工商银行',
        status: 'available'
    };
    
    console.log('🎯 模拟抢单订单:', mockOrder);
    console.log('📡 抢单URL:', orderGrabber.getFullApiUrl('/api/orders/grab'));
    console.log('📦 请求数据:', {
        orderId: mockOrder.id,
        id: mockOrder.id,
        orderNo: mockOrder.id,
        amount: mockOrder.price,
        price: mockOrder.price
    });
    
    return mockOrder;
}

// 6. 完整功能测试套件
function runFullTest() {
    console.log('🚀 开始完整功能测试...');
    console.log('='.repeat(50));
    
    try {
        // 测试配置
        testConfig();
        console.log('');
        
        // 测试API URLs
        testApiUrls();
        console.log('');
        
        // 测试订单筛选
        testOrderFiltering();
        console.log('');
        
        // 测试统计
        testStats();
        console.log('');
        
        // 模拟抢单
        simulateGrabOrder();
        console.log('');
        
        console.log('✅ 所有测试完成！');
        console.log('💡 提示: 使用 orderGrabber.start() 开始实际抢单');
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    }
    
    console.log('='.repeat(50));
}

// 7. 网络连接测试
async function testNetworkConnection() {
    console.log('🧪 测试网络连接...');
    
    try {
        const response = await fetch(window.location.origin + '/api/orders/list', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('📡 网络响应状态:', response.status);
        console.log('📡 响应头:', [...response.headers.entries()]);
        
        if (response.ok) {
            const data = await response.text();
            console.log('📦 响应数据:', data.substring(0, 200) + '...');
        }
        
    } catch (error) {
        console.error('❌ 网络连接测试失败:', error.message);
        console.log('💡 这可能是正常的，因为API路径可能不同');
    }
}

// 8. 快速启动测试
function quickStart() {
    console.log('🚀 快速启动测试模式...');
    
    // 设置测试配置
    testConfig();
    
    // 显示当前状态
    console.log('📊 当前配置:', orderGrabber.config);
    console.log('📊 当前统计:', orderGrabber.getStats());
    
    console.log('');
    console.log('🎮 可用命令:');
    console.log('  runFullTest() - 运行完整测试');
    console.log('  testOrderFiltering() - 测试订单筛选');
    console.log('  testNetworkConnection() - 测试网络连接');
    console.log('  orderGrabber.start() - 开始抢单');
    console.log('  orderGrabber.stop() - 停止抢单');
}

// 自动运行快速启动
if (typeof orderGrabber !== 'undefined') {
    quickStart();
} else {
    console.log('⚠️ 请先加载抢单脚本，然后运行此测试文件');
}

// 导出测试函数（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testConfig,
        testOrderFiltering,
        testApiUrls,
        testStats,
        simulateGrabOrder,
        runFullTest,
        testNetworkConnection,
        quickStart
    };
}
