// ==UserScript==
// @name         智能接单助手 - 多后台版
// @namespace    http://tampermonkey.net/
// @version      1.1.0
// @description  基于价格区间2000-5000的自动接单脚本，在订单有效期内立即接单
// <AUTHOR> Assistant
// @match        https://opc.gt667788.com/*
// @match        https://opc.gt45678.com/*
// @match        https://opc.gt558.com/*
// @match        https://opc.gt6677.com/*
// @match        http://opc.gt667788.com/*
// @match        http://opc.gt45678.com/*
// @match        http://opc.gt558.com/*
// @match        http://opc.gt6677.com/*
// @noframes
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_notification
// @grant        unsafeWindow
// @connect      opc.gt667788.com
// @connect      opc.gt45678.com
// @connect      opc.gt558.com
// @connect      opc.gt6677.com
// @connect      api.gt667788.com
// @connect      api.gt45678.com
// @connect      api.gt558.com
// @connect      api.gt6677.com
// ==/UserScript==

(function() {
    'use strict';

    // 防止重复加载的全局标记
    if (window.orderGrabberLoaded) {
        console.log('🚫 接单助手已加载，跳过重复初始化');
        return;
    }
    window.orderGrabberLoaded = true;

    // {{RIPER-5:
    // Action: Added
    // Task: 油猴抢单脚本基础框架
    // Reason: 创建可配置的抢单脚本模板
    // Principle: 模块化设计，易于维护和扩展
    // Architecture_Note: [AR] 采用配置驱动模式，支持动态参数调整
    // Quality_Check: [LD] 包含错误处理和日志记录机制
    // }}

    // {{START_MODIFICATIONS}}
    
    // 配置管理
    class ConfigManager {
        constructor() {
            this.defaultConfig = {
                minPrice: 2000,       // 最低价格
                maxPrice: 5000,       // 最高价格
                checkInterval: 1000,  // 检查间隔(毫秒) - 缩短以便更频繁检查倒计时
                maxConcurrent: 5,     // 最大并发抢单数
                autoStart: false,     // 自动启动
                enableNotification: true, // 启用通知
                listApi: '/Tenant/GetXPayDepositRequestOrderList?PageSize=2000&PageIndex=1&Role=1&RecordType=0&OrderSourceType=0',     // 列表API路径
                grabApi: '/Tenant/ReceivedXPayOrder',     // 接单API路径
                apiHeaders: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Authorization': ''  // 将在运行时动态获取
                },
                bankFilter: '',       // 银行筛选（备选功能）
                enableBankFilter: false  // 是否启用银行筛选
            };
        }

        getConfig() {
            const saved = GM_getValue('grabOrderConfig', '{}');
            return { ...this.defaultConfig, ...JSON.parse(saved) };
        }

        saveConfig(config) {
            GM_setValue('grabOrderConfig', JSON.stringify(config));
        }
    }

    // 抢单核心逻辑
    class OrderGrabber {
        constructor() {
            this.configManager = new ConfigManager();
            this.config = this.configManager.getConfig();
            this.isRunning = false;
            this.currentGrabs = 0;
            this.intervalId = null;
            this.stats = {
                checked: 0,
                grabbed: 0,
                failed: 0
            };
        }

        // 获取认证token
        getAuthToken() {
            try {
                // 从localStorage获取token
                const token = localStorage.getItem("Token");
                if (token) {
                    return "Bearer " + token;
                } else {
                    console.warn('未找到认证Token，请确保已登录');
                    return '';
                }
            } catch (error) {
                console.error('获取认证Token失败:', error);
                return '';
            }
        }

        // 获取完整API地址
        getFullApiUrl(apiPath) {
            // 根据当前网站域名映射到对应的API域名
            const currentHost = window.location.hostname;
            let apiHost = '';

            if (currentHost.includes('gt667788')) {
                apiHost = 'https://api.gt667788.com';
            } else if (currentHost.includes('gt45678')) {
                apiHost = 'https://api.gt45678.com';
            } else if (currentHost.includes('gt558')) {
                apiHost = 'https://api.gt558.com';
            } else if (currentHost.includes('gt6677')) {
                apiHost = 'https://api.gt6677.com';
            } else {
                // 默认使用第一个API域名
                apiHost = 'https://api.gt667788.com';
                console.warn('未识别的网站域名，使用默认API:', apiHost);
            }

            return apiHost + apiPath;
        }

        // 获取完整的请求头
        getRequestHeaders(isFormData = false) {
            const authToken = this.getAuthToken();
            const currentOrigin = window.location.origin;

            return {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Authorization': authToken,
                'Cache-Control': 'no-cache',
                'Content-Type': isFormData ? 'application/x-www-form-urlencoded' : 'application/json',
                'Origin': currentOrigin,
                'Pragma': 'no-cache',
                'Priority': 'u=1, i',
                'Referer': currentOrigin + '/',
                'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-site',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
                'X-Requested-With': 'XMLHttpRequest'
            };
        }

        // 获取带认证的请求头（向后兼容）
        getAuthHeaders() {
            return this.getRequestHeaders(true);
        }

        // 获取订单列表
        async fetchOrderList() {
            return new Promise((resolve, reject) => {
                if (!this.config.listApi) {
                    reject(new Error('列表API未配置'));
                    return;
                }

                const fullUrl = this.getFullApiUrl(this.config.listApi);
                console.log('正在获取订单列表:', fullUrl);

                GM_xmlhttpRequest({
                    method: 'GET',
                    url: fullUrl,
                    headers: this.getRequestHeaders(false),
                    onload: (response) => {
                        console.log('API响应状态:', response.status);
                        console.log('API响应内容:', response.responseText);

                        try {
                            const data = JSON.parse(response.responseText);
                            console.log('API返回数据:', data);

                            // 根据实际API响应格式处理数据
                            if (data.code === "200" && data.data && data.data.items) {
                                resolve(data.data.items);
                            } else {
                                console.warn('API响应格式异常:', data);
                                resolve([]);
                            }
                        } catch (e) {
                            console.error('解析响应数据失败:', e, response.responseText);
                            reject(new Error('解析响应数据失败: ' + e.message));
                        }
                    },
                    onerror: (error) => {
                        console.error('请求失败:', error);
                        reject(new Error('请求失败: ' + error.message));
                    },
                    ontimeout: () => {
                        console.error('请求超时');
                        reject(new Error('请求超时'));
                    },
                    timeout: 15000
                });
            });
        }

        // 检查订单是否还在有效期内
        parseCountdown(order) {
            // 根据API返回的数据结构，检查订单有效期
            const systemTime = order.systemTime;  // "2025-07-21 23:35:34" - 当前系统时间
            const expiredTime = order.orderExpiredTime;  // "2025-07-22 01:58:30" - 订单过期时间

            if (!systemTime || !expiredTime) {
                return null;
            }

            try {
                // 解析时间
                const currentTime = new Date(systemTime).getTime();
                const expireTime = new Date(expiredTime).getTime();
                const remainingMs = Math.max(0, expireTime - currentTime);

                if (remainingMs > 0) {
                    console.log(`订单 ${order.id} 剩余有效时间: ${Math.round(remainingMs/1000)}秒 (${Math.round(remainingMs/60000)}分钟)`);
                    return {
                        field: 'orderExpiredTime',
                        remainingMs: remainingMs,
                        expireTime: expireTime,
                        systemTime: currentTime,
                        isValid: true  // 订单仍在有效期内
                    };
                } else {
                    console.log(`订单 ${order.id} 已过期`);
                    return {
                        field: 'orderExpiredTime',
                        remainingMs: 0,
                        expireTime: expireTime,
                        systemTime: currentTime,
                        isValid: false  // 订单已过期
                    };
                }
            } catch (e) {
                console.error('解析订单有效期失败:', e, order);
            }

            return null;
        }

        // 筛选符合条件的订单
        filterOrders(orders) {
            if (!Array.isArray(orders)) {
                console.warn('订单数据格式错误，期望数组格式');
                return [];
            }

            const validOrders = [];

            orders.forEach(order => {
                // 使用实际API字段：amount
                const price = parseFloat(order.amount || 0);

                // 价格区间筛选 (2000-5000)
                const priceMatch = price >= this.config.minPrice && price <= this.config.maxPrice;

                // 银行筛选（备选功能）
                let bankMatch = true;
                if (this.config.enableBankFilter && this.config.bankFilter) {
                    const bankName = order.bank || '';
                    bankMatch = bankName.toLowerCase().includes(this.config.bankFilter.toLowerCase());
                }

                if (priceMatch && bankMatch) {
                    // 检查订单有效期
                    const validity = this.parseCountdown(order);

                    if (validity && validity.isValid) {
                        // 订单在有效期内，可以接单
                        validOrders.push(order);
                        console.log(`✅ 发现可接订单: ID=${order.id}, 订单号=${order.orderId}, 价格=${price}, 银行=${order.bank}, 剩余有效期=${Math.round(validity.remainingMs/60000)}分钟`);
                    } else if (validity && !validity.isValid) {
                        console.log(`⏰ 订单已过期: ID=${order.id}, 过期时间=${order.orderExpiredTime}`);
                    }
                }
            });

            // 返回有效的订单
            return validOrders;
        }



        // 执行抢单请求（通用方法）
        async executeGrabRequest(requestConfig) {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'POST',
                    url: requestConfig.url,
                    headers: requestConfig.headers,
                    data: requestConfig.data,
                    onload: (response) => {
                        console.log(`抢单响应:`, response.status, response.responseText);

                        try {
                            const result = JSON.parse(response.responseText);

                            // 根据实际接单API响应格式判断成功
                            const isSuccess = response.status === 200 && (
                                result.code === "200" ||  // 接单API返回的成功码
                                result.code === 200
                            );

                            if (isSuccess) {
                                resolve(result);
                            } else {
                                const errorMsg = result.message || result.msg || result.error || '抢单失败';
                                reject(new Error(errorMsg));
                            }
                        } catch (e) {
                            console.error('解析抢单响应失败:', e, response.responseText);
                            reject(new Error('解析响应失败: ' + e.message));
                        }
                    },
                    onerror: (error) => {
                        console.error('抢单请求失败:', error);
                        reject(new Error('抢单请求失败: ' + error.message));
                    },
                    ontimeout: () => {
                        console.error('抢单请求超时');
                        reject(new Error('抢单请求超时'));
                    },
                    timeout: 5000  // 倒计时抢单需要更快的响应
                });
            });
        }

        // 执行普通接单
        async grabOrder(order) {
            if (!this.config.grabApi) {
                throw new Error('接单API未配置');
            }

            const orderPrice = parseFloat(order.amount || 0);

            console.log(`正在接单: ID=${order.id}, 订单号=${order.orderId}, 价格=${orderPrice}`);

            // 检查认证token
            const authToken = this.getAuthToken();
            if (!authToken) {
                throw new Error('未找到认证Token，请确保已登录');
            }

            // 构建请求配置（根据实际接单API需求）
            const requestData = { Id: order.id };
            const requestConfig = {
                url: this.getFullApiUrl(this.config.grabApi),
                data: new URLSearchParams(requestData).toString(),
                headers: this.getRequestHeaders(true)
            };

            return this.executeGrabRequest(requestConfig);
        }

        // 主循环
        async checkAndGrab() {
            if (this.currentGrabs >= this.config.maxConcurrent) {
                console.log('已达到最大并发数，跳过本次检查');
                return;
            }

            try {
                this.stats.checked++;
                const orders = await this.fetchOrderList();
                const validOrders = this.filterOrders(orders);

                console.log(`检查到 ${validOrders.length} 个符合条件的订单`);

                for (const order of validOrders) {
                    if (this.currentGrabs >= this.config.maxConcurrent) {
                        break;
                    }

                    this.currentGrabs++;
                    this.grabOrder(order)
                        .then((result) => {
                            this.stats.grabbed++;
                            console.log('抢单成功:', order, result);
                            
                            const orderPrice = parseFloat(order.amount || 0);
                            const orderId = order.id;

                            if (this.config.enableNotification) {
                                GM_notification({
                                    title: '🎉 接单成功！',
                                    text: `订单ID: ${orderId}\n订单号: ${order.orderId}\n价格: ¥${orderPrice}\n银行: ${order.bank}\n网站: ${window.location.hostname}`,
                                    timeout: 8000
                                });
                            }
                        })
                        .catch((error) => {
                            this.stats.failed++;
                            console.error('抢单失败:', order, error.message);
                        })
                        .finally(() => {
                            this.currentGrabs--;
                        });
                }
            } catch (error) {
                console.error('检查订单失败:', error.message);
            }
        }

        // 启动抢单
        start() {
            if (this.isRunning) {
                console.log('抢单已在运行中');
                return;
            }

            console.log('启动自动抢单...');
            this.isRunning = true;
            this.intervalId = setInterval(() => {
                this.checkAndGrab();
            }, this.config.checkInterval);

            // 立即执行一次
            this.checkAndGrab();
        }

        // 停止接单
        stop() {
            if (!this.isRunning) {
                console.log('接单未在运行');
                return;
            }

            console.log('停止自动接单...');
            this.isRunning = false;

            // 清理主循环定时器
            if (this.intervalId) {
                clearInterval(this.intervalId);
                this.intervalId = null;
            }

            console.log('✅ 自动接单已停止');
        }

        // 获取统计信息
        getStats() {
            return {
                ...this.stats,
                isRunning: this.isRunning,
                currentGrabs: this.currentGrabs
            };
        }
    }

    // 用户界面
    class UIManager {
        constructor(grabber) {
            this.grabber = grabber;
            this.createUI();
        }

        createUI() {
            // 创建控制面板
            const panel = document.createElement('div');
            panel.id = 'grab-order-panel';
            panel.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                width: 300px;
                background: #fff;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-family: Arial, sans-serif;
                font-size: 14px;
            `;

            panel.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h3 style="margin: 0; color: #333; font-size: 16px;">🚀 智能接单助手</h3>
                    <button id="minimizeBtn" style="background: #2196F3; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">−</button>
                </div>
                <div id="panelContent">
                    <div style="font-size: 12px; color: #666; text-align: center; margin-bottom: 10px;">
                        当前网站: ${window.location.hostname}<br>
                        <span id="tokenStatus">检查认证状态...</span>
                    </div>
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">💰 价格区间 (元):</label>
                    <input type="number" id="minPrice" placeholder="2000" style="width: 70px; margin: 0 5px; padding: 4px;">
                    -
                    <input type="number" id="maxPrice" placeholder="5000" style="width: 70px; margin: 0 5px; padding: 4px;">
                </div>
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">⏱️ 检查间隔:</label>
                    <input type="number" id="checkInterval" placeholder="2000" style="width: 80px; padding: 4px;">
                    <span style="font-size: 12px; color: #666; margin-left: 5px;">毫秒</span>
                </div>
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">🏦 银行筛选 (可选):</label>
                    <input type="checkbox" id="enableBankFilter" style="margin-right: 5px;">
                    <input type="text" id="bankFilter" placeholder="如: 工商银行" style="width: 120px; padding: 4px;" disabled>
                </div>
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">⚡ 并发数:</label>
                    <input type="number" id="maxConcurrent" placeholder="5" style="width: 60px; padding: 4px;" min="1" max="10">
                    <span style="font-size: 12px; color: #666; margin-left: 5px;">个</span>
                </div>

                <div style="margin-bottom: 15px; text-align: center;">
                    <button id="startBtn" style="background: #4CAF50; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; margin-right: 10px; font-weight: bold;">🚀 启动接单</button>
                    <button id="stopBtn" style="background: #f44336; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-weight: bold;">⏹️ 停止</button>
                </div>
                <div id="stats" style="font-size: 12px; color: #666; border-top: 1px solid #eee; padding-top: 10px;">
                    <div style="margin-bottom: 3px;">📊 检查次数: <span id="checkedCount" style="font-weight: bold; color: #333;">0</span></div>
                    <div style="margin-bottom: 3px;">✅ 成功接单: <span id="grabbedCount" style="font-weight: bold; color: #4CAF50;">0</span></div>
                    <div style="margin-bottom: 3px;">❌ 失败次数: <span id="failedCount" style="font-weight: bold; color: #f44336;">0</span></div>
                    <div style="margin-bottom: 3px;">🔄 运行状态: <span id="runningStatus" style="font-weight: bold;">已停止</span></div>
                    <div style="margin-bottom: 3px;">⚡ 当前并发: <span id="currentGrabs" style="font-weight: bold; color: #FF9800;">0</span></div>

                </div>
                </div>
            `;

            document.body.appendChild(panel);
            this.bindEvents();
            this.loadConfig();
            this.startStatsUpdate();
        }

        bindEvents() {
            document.getElementById('startBtn').onclick = () => {
                this.saveConfig();
                this.grabber.start();
            };

            document.getElementById('stopBtn').onclick = () => {
                this.grabber.stop();
            };

            // 最小化按钮
            document.getElementById('minimizeBtn').onclick = () => {
                this.toggleMinimize();
            };

            // 银行筛选开关
            document.getElementById('enableBankFilter').onchange = (e) => {
                const bankInput = document.getElementById('bankFilter');
                bankInput.disabled = !e.target.checked;
                if (!e.target.checked) {
                    bankInput.value = '';
                }
            };
        }

        // 切换最小化状态
        toggleMinimize() {
            const content = document.getElementById('panelContent');
            const btn = document.getElementById('minimizeBtn');
            const panel = document.getElementById('grab-order-panel');

            if (content.style.display === 'none') {
                // 展开
                content.style.display = 'block';
                btn.textContent = '−';
                btn.title = '最小化';
                panel.style.height = 'auto';
            } else {
                // 最小化
                content.style.display = 'none';
                btn.textContent = '+';
                btn.title = '展开';
                panel.style.height = 'auto';
            }
        }

        loadConfig() {
            const config = this.grabber.config;
            document.getElementById('minPrice').value = config.minPrice;
            document.getElementById('maxPrice').value = config.maxPrice;
            document.getElementById('checkInterval').value = config.checkInterval;
            document.getElementById('maxConcurrent').value = config.maxConcurrent;
            document.getElementById('enableBankFilter').checked = config.enableBankFilter;
            document.getElementById('bankFilter').value = config.bankFilter || '';
            document.getElementById('bankFilter').disabled = !config.enableBankFilter;
        }

        saveConfig() {
            const config = {
                ...this.grabber.config,
                minPrice: parseInt(document.getElementById('minPrice').value) || 2000,
                maxPrice: parseInt(document.getElementById('maxPrice').value) || 5000,
                checkInterval: parseInt(document.getElementById('checkInterval').value) || 1000,
                maxConcurrent: parseInt(document.getElementById('maxConcurrent').value) || 5,
                enableBankFilter: document.getElementById('enableBankFilter').checked,
                bankFilter: document.getElementById('bankFilter').value.trim()
            };

            this.grabber.configManager.saveConfig(config);
            this.grabber.config = config;

            console.log('配置已保存:', config);
        }

        startStatsUpdate() {
            setInterval(() => {
                const stats = this.grabber.getStats();
                document.getElementById('checkedCount').textContent = stats.checked;
                document.getElementById('grabbedCount').textContent = stats.grabbed;
                document.getElementById('failedCount').textContent = stats.failed;
                document.getElementById('runningStatus').textContent = stats.isRunning ? '🟢 运行中' : '🔴 已停止';
                document.getElementById('runningStatus').style.color = stats.isRunning ? '#4CAF50' : '#f44336';
                document.getElementById('currentGrabs').textContent = stats.currentGrabs;

                // 更新token状态
                const authToken = this.grabber.getAuthToken();
                const tokenStatus = document.getElementById('tokenStatus');
                if (authToken) {
                    tokenStatus.textContent = '🔑 认证状态: 已登录';
                    tokenStatus.style.color = '#4CAF50';
                } else {
                    tokenStatus.textContent = '⚠️ 认证状态: 未登录';
                    tokenStatus.style.color = '#f44336';
                }
            }, 1000);
        }
    }

    // 网站适配检测
    function detectWebsiteAPI() {
        const hostname = window.location.hostname;
        let apiHost = '';

        if (hostname.includes('gt667788')) {
            apiHost = 'https://api.gt667788.com';
        } else if (hostname.includes('gt45678')) {
            apiHost = 'https://api.gt45678.com';
        } else if (hostname.includes('gt558')) {
            apiHost = 'https://api.gt558.com';
        } else if (hostname.includes('gt6677')) {
            apiHost = 'https://api.gt6677.com';
        } else {
            apiHost = 'https://api.gt667788.com';
        }

        console.log(`检测到网站: ${hostname}`);
        console.log(`对应API域名: ${apiHost}`);
        console.log('列表API:', apiHost + '/Tenant/GetXPayDepositRequestOrderList?PageSize=2000&PageIndex=1&Role=1&RecordType=0&OrderSourceType=0');
        console.log('接单API:', apiHost + '/Tenant/ReceivedXPayOrder');

        return {
            apiHost: apiHost,
            listApi: '/Tenant/GetXPayDepositRequestOrderList?PageSize=2000&PageIndex=1&Role=1&RecordType=0&OrderSourceType=0',
            grabApi: '/Tenant/ReceivedXPayOrder'
        };
    }

    // 检查是否在iframe中
    function isInIframe() {
        try {
            return window.self !== window.top;
        } catch (e) {
            return true;
        }
    }

    // 检查是否已经初始化过
    function isAlreadyInitialized() {
        return document.getElementById('grab-order-panel') !== null;
    }

    // 初始化
    function init() {
        // 防止在iframe中重复加载
        if (isInIframe()) {
            console.log('� 检测到iframe环境，跳过加载以避免重复');
            return;
        }

        // 防止重复初始化
        if (isAlreadyInitialized()) {
            console.log('🚫 脚本已初始化，跳过重复加载');
            return;
        }

        console.log('�🚀 智能接单助手已加载');
        console.log(`当前网站: ${window.location.hostname}`);

        // 检测API路径
        detectWebsiteAPI();

        const grabber = new OrderGrabber();
        const ui = new UIManager(grabber);

        // 暴露到全局作用域，方便调试和手动控制
        unsafeWindow.orderGrabber = grabber;
        unsafeWindow.grabberUI = ui;

        console.log('🎮 调试命令:');
        console.log('  orderGrabber.start() - 启动接单');
        console.log('  orderGrabber.stop() - 停止接单');
        console.log('  orderGrabber.getStats() - 查看统计');
        console.log('  orderGrabber.fetchOrderList() - 手动获取订单列表');

        // 显示欢迎信息
        setTimeout(() => {
            if (grabber.config.enableNotification) {
                GM_notification({
                    title: '🚀 接单助手已就绪',
                    text: `网站: ${window.location.hostname}\n价格区间: ${grabber.config.minPrice}-${grabber.config.maxPrice}元\n模式: 有效期内立即接单`,
                    timeout: 3000
                });
            }
        }, 1000);
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // {{END_MODIFICATIONS}}
})();
