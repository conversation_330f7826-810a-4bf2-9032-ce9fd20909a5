# 🚀 智能抢单助手 - 使用说明

## 📋 功能概述

这是一个专为多个后台网站设计的自动抢单油猴脚本，支持基于价格区间的智能筛选和自动抢单。

### ✨ 主要特性

- **多网站支持**: 支持4个目标后台网站
- **价格筛选**: 自动筛选2000-5000元价格区间的订单
- **倒计时秒抢**: 🔥 **核心功能** - 自动监控订单倒计时，在到期瞬间精确抢单
- **预加载机制**: 提前准备抢单请求，提高成功率
- **银行筛选**: 可选的银行类型筛选功能
- **并发控制**: 支持最多5个订单同时抢单
- **实时监控**: 可配置的检查间隔（默认1秒）
- **智能通知**: 抢单成功时弹出系统通知
- **统计面板**: 实时显示抢单统计信息

## 🎯 支持的网站

- opc.gt667788.com
- opc.gt45678.com  
- opc.gt558.com
- opc.gt6677.com

## 🔧 安装步骤

1. **安装Tampermonkey扩展**
   - Chrome: 从Chrome Web Store安装Tampermonkey
   - Firefox: 从Firefox Add-ons安装Tampermonkey

2. **安装脚本**
   - 打开Tampermonkey管理面板
   - 点击"创建新脚本"
   - 复制粘贴 `grab_orders_userscript.js` 的内容
   - 保存脚本 (Ctrl+S)

3. **访问目标网站**
   - 访问任意一个支持的后台网站
   - 右上角会自动出现控制面板

## 🎮 使用方法

### 基本操作

1. **设置价格区间**
   - 默认: 2000-5000元
   - 可根据需要调整

2. **配置检查间隔**
   - 默认: 2000毫秒 (2秒)
   - 建议不要设置太短，避免对服务器造成压力

3. **启动抢单**
   - 点击"🚀 启动抢单"按钮
   - 脚本开始自动监控和抢单

4. **停止抢单**
   - 点击"⏹️ 停止"按钮

### 高级功能

#### 🕐 倒计时秒抢 (核心功能)

- **自动监控**: 脚本会自动识别有倒计时的订单
- **精确时机**: 在倒计时结束前100毫秒触发抢单
- **预加载**: 提前5秒准备抢单请求数据
- **配置选项**:
  - 勾选"启用倒计时监控"
  - 设置"提前抢单"时间（默认100毫秒）

#### 银行筛选 (可选)

- 勾选"银行筛选"复选框
- 输入银行关键词，如"工商银行"
- 只抢取指定银行的订单

#### 并发控制

- 设置同时抢单的最大数量
- 默认: 5个
- 范围: 1-10个

## 📊 统计信息

控制面板实时显示：
- 📊 检查次数: 总共检查了多少次
- ✅ 成功抢单: 成功抢到的订单数
- ❌ 失败次数: 抢单失败的次数  
- 🔄 运行状态: 当前是否在运行
- ⚡ 当前并发: 正在进行的抢单数

## 🛠️ 调试功能

脚本提供了丰富的调试功能，打开浏览器控制台可以使用：

```javascript
// 手动启动抢单
orderGrabber.start()

// 停止抢单
orderGrabber.stop()

// 查看统计信息
orderGrabber.getStats()

// 手动获取订单列表
orderGrabber.fetchOrderList()

// 查看当前配置
orderGrabber.config
```

## ⚙️ 配置说明

### 默认配置
```javascript
{
    minPrice: 2000,           // 最低价格
    maxPrice: 5000,           // 最高价格
    checkInterval: 2000,      // 检查间隔(毫秒)
    maxConcurrent: 5,         // 最大并发数
    enableNotification: true, // 启用通知
    listApi: '/api/orders/list',  // 列表API路径
    grabApi: '/api/orders/grab',  // 抢单API路径
    enableBankFilter: false,  // 银行筛选开关
    bankFilter: ''           // 银行筛选关键词
}
```

## 🔍 API适配说明

脚本会自动适配不同的API响应格式：

### 订单列表API
- 支持的价格字段: `price`, `amount`, `money`, `orderAmount`, `orderPrice`, `value`
- 支持的ID字段: `id`, `orderId`, `orderNo`
- 支持的银行字段: `bank`, `bankName`, `bankType`

### 抢单API
- 自动构建请求数据，包含订单ID和价格信息
- 支持多种成功响应格式判断

## ⚠️ 注意事项

1. **合规使用**: 请确保在合法合规的范围内使用此脚本
2. **频率控制**: 不要设置过短的检查间隔，避免对服务器造成压力
3. **网络环境**: 确保网络连接稳定，避免抢单失败
4. **浏览器要求**: 建议使用Chrome或Firefox最新版本
5. **API变更**: 如果网站API发生变更，可能需要调整脚本配置

## 🐛 故障排除

### 常见问题

1. **脚本不工作**
   - 检查是否在支持的网站上
   - 确认Tampermonkey已启用
   - 查看浏览器控制台是否有错误信息

2. **无法获取订单列表**
   - 检查网络连接
   - 确认API路径是否正确
   - 查看控制台的API响应信息

3. **抢单总是失败**
   - 检查抢单API路径
   - 确认请求数据格式
   - 查看服务器响应信息

### 调试步骤

1. 打开浏览器开发者工具 (F12)
2. 切换到Console标签
3. 查看脚本输出的日志信息
4. 使用调试命令手动测试功能

## 📞 技术支持

如果遇到问题，请：
1. 查看浏览器控制台的错误信息
2. 检查网站的API响应格式
3. 根据实际情况调整脚本配置

## 🔄 更新日志

### v1.0.0
- 初始版本发布
- 支持4个目标网站
- 价格区间筛选 (2000-5000元)
- 银行筛选功能
- 并发控制
- 实时统计面板
- 系统通知功能
