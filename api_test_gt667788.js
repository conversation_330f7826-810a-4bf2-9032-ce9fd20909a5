// GT667788 API 测试脚本
// 在浏览器控制台中运行，用于测试实际API

// 获取当前网站对应的API域名
function getApiHost() {
    const hostname = window.location.hostname;

    if (hostname.includes('gt667788')) {
        return 'https://api.gt667788.com';
    } else if (hostname.includes('gt45678')) {
        return 'https://api.gt45678.com';
    } else if (hostname.includes('gt558')) {
        return 'https://api.gt558.com';
    } else if (hostname.includes('gt6677')) {
        return 'https://api.gt6677.com';
    } else {
        console.warn('未识别的网站域名，使用默认API: https://api.gt667788.com');
        return 'https://api.gt667788.com';
    }
}

// 测试列表API
async function testListAPI() {
    console.log('🧪 测试列表API...');

    const apiHost = getApiHost();
    const listUrl = apiHost + '/Tenant/GetXPayDepositRequestOrderList?PageSize=2000&PageIndex=1&Role=1&RecordType=0&OrderSourceType=0';

    console.log(`🌐 当前网站: ${window.location.hostname}`);
    console.log(`🔗 API域名: ${apiHost}`);
    
    try {
        const response = await fetch(listUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        console.log('📡 响应状态:', response.status);
        
        if (response.ok) {
            const data = await response.json();
            console.log('📦 API响应数据:', data);
            
            if (data.code === "200" && data.data && data.data.items) {
                const orders = data.data.items;
                console.log(`✅ 获取到 ${orders.length} 个订单`);
                
                // 分析订单数据
                orders.forEach((order, index) => {
                    const price = parseFloat(order.amount || 0);
                    const systemTime = new Date(order.systemTime);
                    const expiredTime = new Date(order.orderExpiredTime);
                    const remainingMs = Math.max(0, expiredTime.getTime() - systemTime.getTime());
                    
                    console.log(`订单 ${index + 1}:`, {
                        id: order.id,
                        orderId: order.orderId,
                        price: price,
                        bank: order.bank,
                        remainingTime: Math.round(remainingMs / 1000) + '秒',
                        expiredTime: order.orderExpiredTime,
                        systemTime: order.systemTime,
                        priceMatch: price >= 2000 && price <= 5000 ? '✅' : '❌'
                    });
                });
                
                // 筛选符合条件的订单
                const validOrders = orders.filter(order => {
                    const price = parseFloat(order.amount || 0);
                    const systemTime = new Date(order.systemTime);
                    const expiredTime = new Date(order.orderExpiredTime);
                    const isNotExpired = expiredTime.getTime() > systemTime.getTime();
                    
                    return price >= 2000 && price <= 5000 && isNotExpired;
                });
                
                console.log(`🎯 符合条件的订单: ${validOrders.length} 个`);
                return validOrders;
                
            } else {
                console.error('❌ API响应格式异常:', data);
            }
        } else {
            console.error('❌ API请求失败:', response.status, response.statusText);
        }
    } catch (error) {
        console.error('❌ 请求异常:', error);
    }
}

// 测试接单API（模拟）
async function testGrabAPI(orderId) {
    console.log('🧪 测试接单API...');
    console.log('⚠️ 这是模拟测试，不会实际发送请求');

    const apiHost = getApiHost();
    const grabUrl = apiHost + '/Tenant/ReceivedXPayOrder';

    const requestData = {
        Id: orderId  // 接单API只需要Id参数
    };

    console.log('📡 接单URL:', grabUrl);
    console.log('📦 请求数据:', requestData);
    console.log('📋 请求头:', {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Requested-With': 'XMLHttpRequest'
    });

    console.log('💡 要实际测试接单，请取消下面代码的注释：');
    console.log(`
    try {
        const formData = new URLSearchParams();
        formData.append('Id', '${orderId}');

        const response = await fetch('${grabUrl}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        });

        const result = await response.json();
        console.log('接单结果:', result);

        // 预期响应格式：
        // {"code":"200","message":"接单成功，请到存款记录进行转账操作","data":"接单成功，请到存款记录进行转账操作"}

    } catch (error) {
        console.error('接单失败:', error);
    }
    `);
}

// 倒计时计算测试
function testCountdownCalculation() {
    console.log('🧪 测试倒计时计算...');
    
    // 模拟订单数据
    const mockOrder = {
        id: "2501734",
        orderId: "1947295120583954441",
        systemTime: "2025-07-21 23:35:34",
        orderExpiredTime: "2025-07-22 01:58:30",
        amount: 3000,
        bank: "中国银行"
    };
    
    console.log('📦 模拟订单:', mockOrder);
    
    try {
        const systemTime = new Date(mockOrder.systemTime).getTime();
        const expiredTime = new Date(mockOrder.orderExpiredTime).getTime();
        const remainingMs = Math.max(0, expiredTime - systemTime);
        
        console.log('⏰ 倒计时计算结果:');
        console.log('  系统时间:', mockOrder.systemTime, '(', systemTime, ')');
        console.log('  过期时间:', mockOrder.orderExpiredTime, '(', expiredTime, ')');
        console.log('  剩余毫秒:', remainingMs);
        console.log('  剩余秒数:', Math.round(remainingMs / 1000));
        console.log('  剩余分钟:', Math.round(remainingMs / 60000));
        
        // 计算抢单时机
        const advanceMs = 100; // 提前100毫秒
        const grabTime = Math.max(0, remainingMs - advanceMs);
        console.log(`  抢单延迟: ${grabTime}毫秒 (提前${advanceMs}毫秒)`);
        
        return {
            remainingMs,
            grabDelay: grabTime
        };
        
    } catch (error) {
        console.error('❌ 倒计时计算失败:', error);
    }
}

// 完整测试流程
async function runFullAPITest() {
    console.log('🚀 开始完整API测试...');
    console.log('='.repeat(60));
    
    // 1. 测试列表API
    const orders = await testListAPI();
    console.log('');
    
    // 2. 测试倒计时计算
    testCountdownCalculation();
    console.log('');
    
    // 3. 如果有订单，测试接单API
    if (orders && orders.length > 0) {
        const firstOrder = orders[0];
        console.log('🎯 使用第一个符合条件的订单测试接单API:');
        await testGrabAPI(firstOrder.id);
    } else {
        console.log('⚠️ 没有符合条件的订单，使用模拟数据测试接单API:');
        await testGrabAPI("2501734");
    }
    
    console.log('');
    console.log('✅ 测试完成！');
    console.log('='.repeat(60));
}

// 监控订单变化
let monitorInterval = null;

function startOrderMonitor() {
    console.log('🔄 开始监控订单变化...');
    
    let lastOrderCount = 0;
    let lastValidCount = 0;
    
    monitorInterval = setInterval(async () => {
        try {
            const orders = await testListAPI();
            if (orders) {
                const validOrders = orders.filter(order => {
                    const price = parseFloat(order.amount || 0);
                    return price >= 2000 && price <= 5000;
                });
                
                if (orders.length !== lastOrderCount || validOrders.length !== lastValidCount) {
                    console.log(`📊 订单变化: 总数 ${orders.length} (${orders.length - lastOrderCount >= 0 ? '+' : ''}${orders.length - lastOrderCount}), 符合条件 ${validOrders.length} (${validOrders.length - lastValidCount >= 0 ? '+' : ''}${validOrders.length - lastValidCount})`);
                    
                    lastOrderCount = orders.length;
                    lastValidCount = validOrders.length;
                }
            }
        } catch (error) {
            console.error('❌ 监控过程中出错:', error);
        }
    }, 5000); // 每5秒检查一次
    
    console.log('💡 使用 stopOrderMonitor() 停止监控');
}

function stopOrderMonitor() {
    if (monitorInterval) {
        clearInterval(monitorInterval);
        monitorInterval = null;
        console.log('⏹️ 订单监控已停止');
    } else {
        console.log('⚠️ 监控未在运行');
    }
}

// 导出测试函数
console.log('🎮 可用的测试命令:');
console.log('  runFullAPITest() - 运行完整测试');
console.log('  testListAPI() - 测试列表API');
console.log('  testCountdownCalculation() - 测试倒计时计算');
console.log('  startOrderMonitor() - 开始监控订单');
console.log('  stopOrderMonitor() - 停止监控订单');

// 如果在模块环境中，导出函数
if (typeof window !== 'undefined') {
    window.testListAPI = testListAPI;
    window.testGrabAPI = testGrabAPI;
    window.testCountdownCalculation = testCountdownCalculation;
    window.runFullAPITest = runFullAPITest;
    window.startOrderMonitor = startOrderMonitor;
    window.stopOrderMonitor = stopOrderMonitor;
}
