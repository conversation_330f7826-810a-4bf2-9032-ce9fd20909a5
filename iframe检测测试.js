// iframe检测测试脚本
// 在浏览器控制台中运行，测试iframe检测逻辑

console.log('🧪 iframe检测测试脚本已加载');

// 检查是否在iframe中
function isInIframe() {
    try {
        return window.self !== window.top;
    } catch (e) {
        return true;
    }
}

// 检查是否已经初始化过
function isAlreadyInitialized() {
    return document.getElementById('grab-order-panel') !== null;
}

// 获取页面信息
function getPageInfo() {
    return {
        url: window.location.href,
        hostname: window.location.hostname,
        isIframe: isInIframe(),
        hasPanel: isAlreadyInitialized(),
        windowSelf: window.self,
        windowTop: window.top,
        isSameWindow: window.self === window.top,
        globalFlag: window.orderGrabberLoaded || false
    };
}

// 测试iframe检测
function testIframeDetection() {
    console.log('🔍 测试iframe检测逻辑...');
    console.log('='.repeat(50));
    
    const info = getPageInfo();
    
    console.log('📍 页面信息:');
    console.log('  URL:', info.url);
    console.log('  域名:', info.hostname);
    console.log('  是否在iframe中:', info.isIframe ? '✅ 是' : '❌ 否');
    console.log('  是否已有控制面板:', info.hasPanel ? '✅ 是' : '❌ 否');
    console.log('  全局加载标记:', info.globalFlag ? '✅ 已设置' : '❌ 未设置');
    
    console.log('');
    console.log('🔧 技术细节:');
    console.log('  window.self === window.top:', info.isSameWindow);
    console.log('  window.self:', info.windowSelf);
    console.log('  window.top:', info.windowTop);
    
    console.log('');
    console.log('📊 检测结果:');
    if (info.isIframe) {
        console.log('  🚫 当前在iframe中，脚本应该跳过加载');
    } else {
        console.log('  ✅ 当前在主页面中，脚本可以正常加载');
    }
    
    if (info.hasPanel) {
        console.log('  ⚠️ 检测到已有控制面板，可能存在重复加载');
    } else {
        console.log('  ✅ 未检测到控制面板，可以安全初始化');
    }
    
    console.log('='.repeat(50));
    
    return info;
}

// 模拟脚本初始化逻辑
function simulateScriptInit() {
    console.log('🤖 模拟脚本初始化逻辑...');
    
    // 检查全局标记
    if (window.orderGrabberLoaded) {
        console.log('🚫 全局标记检查: 脚本已加载，跳过重复初始化');
        return false;
    }
    
    // 检查iframe
    if (isInIframe()) {
        console.log('🚫 iframe检查: 检测到iframe环境，跳过加载以避免重复');
        return false;
    }
    
    // 检查是否已初始化
    if (isAlreadyInitialized()) {
        console.log('🚫 重复检查: 脚本已初始化，跳过重复加载');
        return false;
    }
    
    console.log('✅ 所有检查通过，可以安全初始化脚本');
    
    // 模拟设置全局标记
    window.orderGrabberLoaded = true;
    console.log('🏷️ 已设置全局加载标记');
    
    return true;
}

// 检查所有iframe
function checkAllFrames() {
    console.log('🔍 检查页面中的所有iframe...');
    console.log('='.repeat(50));
    
    const frames = document.querySelectorAll('iframe');
    console.log(`📊 发现 ${frames.length} 个iframe`);
    
    frames.forEach((frame, index) => {
        console.log(`iframe ${index + 1}:`);
        console.log('  src:', frame.src || '(无src)');
        console.log('  id:', frame.id || '(无id)');
        console.log('  name:', frame.name || '(无name)');
        console.log('  width:', frame.width || 'auto');
        console.log('  height:', frame.height || 'auto');
        console.log('');
    });
    
    if (frames.length === 0) {
        console.log('✅ 页面中没有iframe，不会出现重复加载问题');
    } else {
        console.log('⚠️ 页面中有iframe，需要确保脚本正确处理');
    }
    
    console.log('='.repeat(50));
    
    return frames;
}

// 完整的iframe测试
function runFullIframeTest() {
    console.log('🚀 开始完整的iframe检测测试...');
    console.log('='.repeat(60));
    
    // 1. 基础检测
    console.log('1️⃣ 基础iframe检测');
    const pageInfo = testIframeDetection();
    console.log('');
    
    // 2. 检查所有iframe
    console.log('2️⃣ 页面iframe扫描');
    const frames = checkAllFrames();
    console.log('');
    
    // 3. 模拟初始化
    console.log('3️⃣ 模拟脚本初始化');
    const canInit = simulateScriptInit();
    console.log('');
    
    // 4. 汇总结果
    console.log('📊 测试结果汇总:');
    console.log('  当前环境:', pageInfo.isIframe ? 'iframe' : '主页面');
    console.log('  iframe数量:', frames.length);
    console.log('  可以初始化:', canInit ? '✅ 是' : '❌ 否');
    console.log('  重复风险:', frames.length > 0 && !pageInfo.isIframe ? '⚠️ 中等' : '✅ 低');
    
    console.log('');
    console.log('💡 建议:');
    if (pageInfo.isIframe) {
        console.log('  ✅ 当前在iframe中，脚本正确跳过加载');
    } else if (frames.length > 0) {
        console.log('  ⚠️ 主页面包含iframe，确保使用@noframes指令');
        console.log('  ⚠️ 建议在脚本中添加iframe检测逻辑');
    } else {
        console.log('  ✅ 环境正常，可以安全运行脚本');
    }
    
    console.log('='.repeat(60));
    
    return {
        pageInfo,
        frames: frames.length,
        canInit
    };
}

// 导出测试函数
console.log('🎮 可用的测试命令:');
console.log('  testIframeDetection() - 测试iframe检测逻辑');
console.log('  checkAllFrames() - 检查页面中的所有iframe');
console.log('  simulateScriptInit() - 模拟脚本初始化过程');
console.log('  runFullIframeTest() - 运行完整测试');
console.log('  getPageInfo() - 获取当前页面信息');

// 全局导出
if (typeof window !== 'undefined') {
    window.testIframeDetection = testIframeDetection;
    window.checkAllFrames = checkAllFrames;
    window.simulateScriptInit = simulateScriptInit;
    window.runFullIframeTest = runFullIframeTest;
    window.getPageInfo = getPageInfo;
}

// 自动运行基础检测
console.log('');
testIframeDetection();
