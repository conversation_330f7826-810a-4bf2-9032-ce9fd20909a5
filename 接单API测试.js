// 接单API完整测试脚本
// 在浏览器控制台中运行

console.log('🚀 接单API测试脚本已加载');

// 获取当前网站对应的API域名
function getApiHost() {
    const hostname = window.location.hostname;

    if (hostname.includes('gt667788')) {
        return 'https://api.gt667788.com';
    } else if (hostname.includes('gt45678')) {
        return 'https://api.gt45678.com';
    } else if (hostname.includes('gt558')) {
        return 'https://api.gt558.com';
    } else if (hostname.includes('gt6677')) {
        return 'https://api.gt6677.com';
    } else {
        // 默认使用第一个
        console.warn('未识别的网站域名，使用默认API: https://api.gt667788.com');
        return 'https://api.gt667788.com';
    }
}

// 获取认证token
function getAuthToken() {
    try {
        const token = localStorage.getItem("Token");
        if (token) {
            return "Bearer " + token;
        } else {
            console.warn('未找到认证Token，请确保已登录');
            return '';
        }
    } catch (error) {
        console.error('获取认证Token失败:', error);
        return '';
    }
}

// 获取完整的请求头
function getRequestHeaders(isFormData = false) {
    const authToken = getAuthToken();
    const currentOrigin = window.location.origin;

    return {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Authorization': authToken,
        'Cache-Control': 'no-cache',
        'Content-Type': isFormData ? 'application/x-www-form-urlencoded' : 'application/json',
        'Origin': currentOrigin,
        'Pragma': 'no-cache',
        'Priority': 'u=1, i',
        'Referer': currentOrigin + '/',
        'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest'
    };
}

// 实际测试接单API
async function testRealGrabAPI(orderId) {
    console.log(`🧪 实际测试接单API - 订单ID: ${orderId}`);

    const apiHost = getApiHost();
    const grabUrl = apiHost + '/Tenant/ReceivedXPayOrder';

    console.log(`🌐 当前网站: ${window.location.hostname}`);
    console.log(`🔗 API域名: ${apiHost}`);
    
    try {
        // 构建表单数据
        const formData = new URLSearchParams();
        formData.append('Id', orderId);
        
        const authToken = getAuthToken();
        console.log('🔑 认证Token:', authToken ? '已获取' : '未找到');
        console.log('📡 发送接单请求...');
        console.log('URL:', grabUrl);
        console.log('参数:', `Id=${orderId}`);

        const response = await fetch(grabUrl, {
            method: 'POST',
            headers: getRequestHeaders(true),
            body: formData
        });
        
        console.log('📡 响应状态:', response.status);
        
        if (response.ok) {
            const result = await response.json();
            console.log('📦 接单响应:', result);
            
            // 检查响应格式
            if (result.code === "200") {
                console.log('✅ 接单成功!');
                console.log('💬 消息:', result.message);
                console.log('📄 数据:', result.data);
                return { success: true, result };
            } else {
                console.log('❌ 接单失败');
                console.log('错误码:', result.code);
                console.log('错误信息:', result.message);
                return { success: false, result };
            }
        } else {
            console.error('❌ HTTP请求失败:', response.status, response.statusText);
            return { success: false, error: `HTTP ${response.status}` };
        }
        
    } catch (error) {
        console.error('❌ 接单异常:', error);
        return { success: false, error: error.message };
    }
}

// 获取可用订单并尝试接单
async function findAndGrabOrder() {
    console.log('🔍 查找可用订单并尝试接单...');
    
    // 1. 获取订单列表
    const apiHost = getApiHost();
    const listUrl = apiHost + '/Tenant/GetXPayDepositRequestOrderList?PageSize=2000&PageIndex=1&Role=1&RecordType=0&OrderSourceType=0';
    
    try {
        console.log('📡 获取订单列表...');
        const authToken = getAuthToken();
        const response = await fetch(listUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Authorization': authToken
            }
        });
        
        if (!response.ok) {
            console.error('❌ 获取订单列表失败:', response.status);
            return;
        }
        
        const data = await response.json();
        
        if (data.code !== "200" || !data.data || !data.data.items) {
            console.error('❌ 订单列表响应格式异常:', data);
            return;
        }
        
        const orders = data.data.items;
        console.log(`📊 获取到 ${orders.length} 个订单`);
        
        // 2. 筛选符合条件的订单
        const validOrders = orders.filter(order => {
            const price = parseFloat(order.amount || 0);
            const systemTime = new Date(order.systemTime);
            const expiredTime = new Date(order.orderExpiredTime);
            const isNotExpired = expiredTime.getTime() > systemTime.getTime();
            
            return price >= 2000 && price <= 5000 && isNotExpired;
        });
        
        console.log(`🎯 符合条件的订单: ${validOrders.length} 个`);
        
        if (validOrders.length === 0) {
            console.log('⚠️ 没有符合条件的订单');
            return;
        }
        
        // 3. 选择第一个订单进行接单测试
        const targetOrder = validOrders[0];
        console.log('🎯 选择订单进行接单测试:');
        console.log('  ID:', targetOrder.id);
        console.log('  订单号:', targetOrder.orderId);
        console.log('  价格:', targetOrder.amount);
        console.log('  银行:', targetOrder.bank);
        console.log('  过期时间:', targetOrder.orderExpiredTime);
        
        // 4. 执行接单
        const result = await testRealGrabAPI(targetOrder.id);
        
        if (result.success) {
            console.log('🎉 接单测试成功完成!');
        } else {
            console.log('❌ 接单测试失败:', result.error || result.result);
        }
        
        return result;
        
    } catch (error) {
        console.error('❌ 查找订单过程中出错:', error);
    }
}

// 批量测试多个订单
async function batchTestOrders(maxCount = 3) {
    console.log(`🔄 批量测试接单 (最多${maxCount}个订单)...`);
    
    // 获取订单列表
    const apiHost = getApiHost();
    const listUrl = apiHost + '/Tenant/GetXPayDepositRequestOrderList?PageSize=2000&PageIndex=1&Role=1&RecordType=0&OrderSourceType=0';
    
    try {
        const response = await fetch(listUrl);
        const data = await response.json();
        
        if (data.code !== "200" || !data.data || !data.data.items) {
            console.error('❌ 获取订单列表失败');
            return;
        }
        
        const orders = data.data.items;
        const validOrders = orders.filter(order => {
            const price = parseFloat(order.amount || 0);
            return price >= 2000 && price <= 5000;
        }).slice(0, maxCount);
        
        console.log(`📊 将测试 ${validOrders.length} 个订单`);
        
        const results = [];
        
        for (let i = 0; i < validOrders.length; i++) {
            const order = validOrders[i];
            console.log(`\n--- 测试订单 ${i + 1}/${validOrders.length} ---`);
            
            const result = await testRealGrabAPI(order.id);
            results.push({
                order: order,
                result: result
            });
            
            // 间隔1秒避免请求过快
            if (i < validOrders.length - 1) {
                console.log('⏳ 等待1秒...');
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        // 汇总结果
        console.log('\n📊 批量测试结果汇总:');
        const successCount = results.filter(r => r.result.success).length;
        const failCount = results.length - successCount;
        
        console.log(`✅ 成功: ${successCount} 个`);
        console.log(`❌ 失败: ${failCount} 个`);
        
        results.forEach((item, index) => {
            const status = item.result.success ? '✅' : '❌';
            console.log(`  ${status} 订单${index + 1} (ID: ${item.order.id}): ${item.result.success ? '成功' : item.result.error || '失败'}`);
        });
        
        return results;
        
    } catch (error) {
        console.error('❌ 批量测试过程中出错:', error);
    }
}

// 模拟脚本的接单流程
async function simulateScriptFlow() {
    console.log('🤖 模拟脚本接单流程...');
    
    try {
        // 1. 获取订单列表
        console.log('1️⃣ 获取订单列表...');
        const apiHost = getApiHost();
        const listUrl = apiHost + '/Tenant/GetXPayDepositRequestOrderList?PageSize=2000&PageIndex=1&Role=1&RecordType=0&OrderSourceType=0';
        const listResponse = await fetch(listUrl);
        const listData = await listResponse.json();
        
        if (listData.code !== "200") {
            console.error('❌ 获取订单列表失败');
            return;
        }
        
        const orders = listData.data.items;
        console.log(`📊 获取到 ${orders.length} 个订单`);
        
        // 2. 筛选订单
        console.log('2️⃣ 筛选符合条件的订单...');
        const validOrders = orders.filter(order => {
            const price = parseFloat(order.amount || 0);
            const systemTime = new Date(order.systemTime);
            const expiredTime = new Date(order.orderExpiredTime);
            const remainingMs = expiredTime.getTime() - systemTime.getTime();
            
            return price >= 2000 && price <= 5000 && remainingMs > 0;
        });
        
        console.log(`🎯 符合条件的订单: ${validOrders.length} 个`);
        
        if (validOrders.length === 0) {
            console.log('⚠️ 没有符合条件的订单，模拟结束');
            return;
        }
        
        // 3. 分析倒计时
        console.log('3️⃣ 分析订单倒计时...');
        validOrders.forEach((order, index) => {
            const systemTime = new Date(order.systemTime);
            const expiredTime = new Date(order.orderExpiredTime);
            const remainingMs = expiredTime.getTime() - systemTime.getTime();
            const remainingSeconds = Math.round(remainingMs / 1000);
            
            console.log(`  订单${index + 1}: ID=${order.id}, 价格=${order.amount}, 剩余=${remainingSeconds}秒`);
        });
        
        // 4. 选择最快到期的订单进行接单
        const targetOrder = validOrders.reduce((prev, current) => {
            const prevRemaining = new Date(prev.orderExpiredTime).getTime() - new Date(prev.systemTime).getTime();
            const currentRemaining = new Date(current.orderExpiredTime).getTime() - new Date(current.systemTime).getTime();
            return currentRemaining < prevRemaining ? current : prev;
        });
        
        console.log('4️⃣ 选择最快到期的订单进行接单...');
        console.log('目标订单:', {
            id: targetOrder.id,
            orderId: targetOrder.orderId,
            amount: targetOrder.amount,
            bank: targetOrder.bank,
            expiredTime: targetOrder.orderExpiredTime
        });
        
        // 5. 执行接单
        console.log('5️⃣ 执行接单...');
        const result = await testRealGrabAPI(targetOrder.id);
        
        if (result.success) {
            console.log('🎉 模拟接单流程成功完成!');
            console.log('📋 接单结果:', result.result);
        } else {
            console.log('❌ 模拟接单流程失败:', result.error);
        }
        
        return result;
        
    } catch (error) {
        console.error('❌ 模拟流程出错:', error);
    }
}

// 导出测试函数
console.log('🎮 可用的测试命令:');
console.log('  testRealGrabAPI("订单ID") - 测试指定订单的接单');
console.log('  findAndGrabOrder() - 自动查找并接单第一个符合条件的订单');
console.log('  batchTestOrders(3) - 批量测试多个订单接单');
console.log('  simulateScriptFlow() - 模拟完整的脚本接单流程');

// 全局导出
if (typeof window !== 'undefined') {
    window.testRealGrabAPI = testRealGrabAPI;
    window.findAndGrabOrder = findAndGrabOrder;
    window.batchTestOrders = batchTestOrders;
    window.simulateScriptFlow = simulateScriptFlow;
}
